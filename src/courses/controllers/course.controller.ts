import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { CourseService } from "../services/course.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CheckedInDto, CourseListDto, CustomerListDto, CustomerListForSchedulingDto, EnrollSchedulingDto, UpdateCourseStatusDto, AutoEnrollSchedulingDto } from "src/courses/dto/courses.dto";
import { CreateSchedulingDataDto, SchedulingListDto, UpdateSchedulingDataDto } from "src/courses/dto/scheduling.dto";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";

@ApiTags("module.scheduling.course")
@ApiBearerAuth()
@Controller("course")
export class CourseController {
    constructor(private courseService: CourseService) { }
    @Post("/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Course list" })
    async getCourseList(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() courseListDto: CourseListDto
    ): Promise<any> {
        const output = await this.courseService.getCourseList(organizationId, courseListDto);
        return output;
    }

    @Get("/details/:courseId")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Course Details" })
    async getCourseDetail(
        @Param("courseId") courseId: string): Promise<any> {
        let output = await this.courseService.getCourseDetail(courseId);
        return output;
    }

    @Get("/public/details/:courseId")
    @ApiOperation({ summary: "Course Details" })
    async getCourseDetailPublic(
        @Param("courseId") courseId: string): Promise<any> {
        let output = await this.courseService.getCourseDetailPublic(courseId);
        return output;
    }

    @Post("/customer-list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Customer list" })
    async getCustomerList(@GetUser() user: any, @Body() customerListDto: CustomerListDto): Promise<any> {
        const output = await this.courseService.getCustomerList(user, customerListDto);
        return output;
    }

    @Post("/scheduling-customer-list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Customer list" })
    async getCustomerListForScheduling(@GetUser() user: any, @Body() customerListDto: CustomerListForSchedulingDto): Promise<any> {
        const output = await this.courseService.getCustomerListForScheduling(user, customerListDto);
        return output;
    }

    @Post("/scheduling-list")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Customer list" })
    async getSchedulingList(@GetUser() user: any, @Body() schedulingListDto: SchedulingListDto): Promise<any> {
        const output = await this.courseService.getSchedulingList(user, schedulingListDto);
        return output;
    }

    @Post("/public/scheduling-list")
    @ApiOperation({ summary: "Customer list" })
    async getSchedulingListPublic(@Body() schedulingListDto: SchedulingListDto): Promise<any> {
        const output = await this.courseService.getSchedulingListPublic(schedulingListDto);
        return output;
    }

    @Patch("/update-status")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update  status" })
    async updateCourseStatus(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() updateCourseStatusDto: UpdateCourseStatusDto): Promise<any> {
        const output = await this.courseService.updateCourseStatus(updateCourseStatusDto, organizationId);
        return output;
    }

    @Post("/create-schedule")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create Course Schedule" })
    async createCourseScheduling(@GetUser() user: any, @Body() createSchedulingDto: CreateSchedulingDataDto): Promise<any> {
        const output = await this.courseService.createCourseScheduling(createSchedulingDto, user);
        return output;
    }

    @Patch("/update-schedule")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update Course Schedule" })
    async updateCourseScheduling(@GetUser() user: any, @Body() updateSchedulingDto: UpdateSchedulingDataDto): Promise<any> {
        const output = await this.courseService.updateCourseScheduling(updateSchedulingDto, user);
        return output;
    }

    @Get("/scheduling-details/:schedulingId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Scheduling Details" })
    async getSchedulingDetails(@GetUser() user: any, @Param("schedulingId") schedulingId: string): Promise<any> {
        let output = await this.courseService.getSchedulingDetails(user, schedulingId);
        return output;
    }

    @Delete("/delete-schedule/:schedulingId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Delete Scheduling" })
    async deleteSchedule(@GetUser() user: any, @Param("schedulingId") schedulingId: string): Promise<any> {
        let output = await this.courseService.deleteSchedule(user, schedulingId);
        return output;
    }

    @Patch("/cancel-schedule/:schedulingId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Scheduling Details" })
    async cancelSchedule(@GetUser() user: any, @Param("schedulingId") schedulingId: string): Promise<any> {
        let output = await this.courseService.cancelSchedule(user, schedulingId);
        return output;
    }

    @Post("/enroll-schedule")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Enroll Schedule" })
    async enrollScheduling(@GetUser() user: any, @Body() enrollSchedulingDto: EnrollSchedulingDto): Promise<any> {
        const output = await this.courseService.enrollScheduling(enrollSchedulingDto, user);
        return output;
    }

    @Post("/auto-enroll-schedule")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({
        summary: "Auto Enroll Schedule",
        description: "Auto enroll clients in course schedules with generic duplicate handling. Set stopOnDuplicate=true to get errors for duplicates, or false to skip duplicates and continue."
    })
    async autoEnrollScheduling(@GetUser() user: any, @Body() autoEnrollDto: AutoEnrollSchedulingDto): Promise<any> {
        const output = await this.courseService.autoEnrollScheduling(autoEnrollDto, user);
        return output;
    }

    @Post("/check-in")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Check In" })
    async checkIn(@GetUser() user: any, @Body() checkedInDto: CheckedInDto): Promise<any> {
        const output = await this.courseService.checkIn(user, checkedInDto);
        return output;
    }

    @Delete("/delete-enrollment/:enrollmentId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Delete Enrollment" })
    async deleteEnrollment(@GetUser() user: any, @Param("enrollmentId") enrollmentId: string): Promise<any> {
        let output = await this.courseService.deleteEnrollment(user, enrollmentId);
        return output;
    }
}
