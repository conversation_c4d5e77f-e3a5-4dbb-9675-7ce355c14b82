import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_POLICY_STATUS_CODE_ERROR } from 'src/policy/enums/policy.status-code.enum';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { ENUM_AUTH_STATUS_CODE_ERROR } from 'src/auth/enums/auth.status-code.enum';
import { PERMISSION_META_VALIDATE_DELEGATE_USER_KEY, PERMISSIONS_META_KEY } from 'src/policy/constants/policy.constant';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { IUserDocument } from 'src/users/interfaces/user.interface';
import { PermissionService } from '../services/permission.service';

@Injectable()
export class PolicyAbilityGuard implements CanActivate {
    constructor(
        private readonly reflector: Reflector,
        private readonly permissionService: PermissionService
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<IRequestApp>();
        const requiredPermissions = this.reflector.get<ENUM_PERMISSION_TYPE[]>(PERMISSIONS_META_KEY, context.getHandler()) || [];
        const shouldValidateDelegateUser = !!this.reflector.get<boolean | undefined>(PERMISSION_META_VALIDATE_DELEGATE_USER_KEY, context.getHandler());

        // Set
        request.__permissionSet = new Set<ENUM_PERMISSION_TYPE>();

        const { __user, user } = context
            .switchToHttp()
            .getRequest<IRequestApp>() as { __user: IUserDocument, user: any };

        if (!__user && !user) {
            throw new ForbiddenException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
            });
        }
        const { type } = __user?.role || user?.role;

        if (shouldValidateDelegateUser && !(type === ENUM_ROLE_TYPE.SUPER_ADMIN || type === ENUM_ROLE_TYPE.ORGANIZATION)) {
            // Validate delegate user
            const { __delegateUser } = context
                .switchToHttp()
                .getRequest<IRequestApp>() as { __delegateUser: IUserDocument };

            if (!__delegateUser) {
                throw new ForbiddenException({
                    statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                    message: 'auth.error.sessionExpired',
                });
            }

            return this.validateUser(request, __delegateUser, requiredPermissions);
        }

        return this.validateUser(request, __user, requiredPermissions);
    }


    /**
     * This is to check users permissions
     * @param request Express Resquest
     * @param user User Document
     * @param requiredPermissions Required permissions
     * @returns Boolean
     */
    private async validateUser(request: IRequestApp, user: IUserDocument, requiredPermissions: ENUM_PERMISSION_TYPE[]) {
        const { type } = user.role;

        // Super admin bypass all policy checks
        if (type === ENUM_ROLE_TYPE.SUPER_ADMIN || type === ENUM_ROLE_TYPE.ORGANIZATION) {
            const permissions = await this.permissionService.getAllPermissionsType()
            request.__permissionSet = new Set(permissions);
            return true;
        }

        [...user.role?.policies || [], ...user?.assignedPolicies || []]
            .filter(policy => ![...user.restrictedPolicies || []].some(restrictedPolicy =>
                restrictedPolicy._id.toString() === policy._id.toString()
            ))
            .flatMap(policy => policy.permissions || [])
            .map(permission => {
                if (!permission.isActive) return;
                request.__permissionSet.add(permission.type)
            });

        // If no policies are required for this route
        if (requiredPermissions.length === 0) {
            return true
        }

        const hasRequiredPermissions = requiredPermissions.every(
            (requiredPolicy: ENUM_PERMISSION_TYPE) => request.__permissionSet.has(requiredPolicy)
        );

        if (!hasRequiredPermissions) {
            throw new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_FORBIDDEN,
                message: 'policy.error.abilityForbidden',
            });
        }
        return true;
    }
}

