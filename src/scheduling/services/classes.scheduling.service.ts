import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import moment from "moment";
import { TransactionService } from "src/utils/services/transaction.service";
import { AppointmentTypeDocument, Services } from "src/organization/schemas/services.schema";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ClassType } from "src/utils/enums/class-type.enum";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { ClassScheduleCreateDto } from "../dto/class.schedule.create.dto";
import { SchedulingService } from "./scheduling.service";
import { Facility } from "src/facility/schemas/facility.schema";
import { Purchase, PurchaseDocument } from "src/users/schemas/purchased-packages.schema";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { IDatabaseFindAllOptions } from "src/common/database/interfaces/database.interface";
import { Enrollment } from "src/courses/schemas/enrollment.schema";
import { ClassCancelEnrollDto, ClassScheduleEnrollDto } from "../dto/class.schedule.enroll.dto";
import { Clients } from "src/users/schemas/clients.schema";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { ClassScheduleGetUserForEnrollmentDto } from "../dto/class.schedule.get.dto";
import { CheckedInDto } from "src/courses/dto/courses.dto";
import { ClassScheduleUpdateDto, ClassScheduleUpdateDtoV1 } from "../dto/class.schedule.update.dto";
import { MailService } from "src/mail/services/mail.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { IScheduleDates } from "../interfaces/class.schedule.interface";
import { Room } from "src/room/schema/room.schema";
import { GetScheduleDetailsClassDto } from "../dto/GetScheduleDetailsClassDto";
import { addDays, isAfter } from "date-fns";
import { CancelRecurringScheduleDto } from "../dto/CancelRecurringScheduleDto";
import { WaitTimeGatewayService } from "src/wait-time/gateway/wait-time-gateway.service";
import { DateTime } from "luxon";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { DateRange } from "src/utils/enums/date-range-enum";
import { GetScheduleDetailsDto } from "../dto/get-schedules-recurring.dto";


@Injectable()
export class ClassesSchedulingService {
    constructor(
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(Enrollment.name) private EnrollmentModel: Model<Enrollment>,
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        @InjectModel(Room.name) private RoomModel: Model<Room>,
        private readonly schedulingService: SchedulingService,
        private readonly transactionService: TransactionService,
        private readonly paginationService: PaginationService,
        private readonly mailService: MailService,
        private waitTimeGatewayService: WaitTimeGatewayService,
        

    ) { }

    async getEnrolledCount(schedulingIds: Types.ObjectId[]): Promise<{ schedulingId: number; count: number }[]> {
        return this.EnrollmentModel.aggregate([
            { $match: { schedulingId: { $in: schedulingIds } } },
            { $group: { _id: "$schedulingId", count: { $sum: 1 } } },
            { $project: { _id: 0, schedulingId: "$_id", count: 1 } },
        ]);
    }

    async getCheckinCount(schedulingIds: Types.ObjectId[]): Promise<{ schedulingId: number; count: number }[]> {
        return this.EnrollmentModel.aggregate([
            { $match: { schedulingId: { $in: schedulingIds }, isCheckedIn: true } },
            { $group: { _id: "$schedulingId", count: { $sum: 1 } } },
            { $project: { _id: 0, schedulingId: "$_id", count: 1 } },
        ]);
    }

    async getClassesList(filter: any, options: any, isClient = false): Promise<{
        data: any[];
        count: number;
    }> {
        let list = [];
        const schedulingData = await this.SchedulingModel.find(
            filter,
        ).sort({
            ...this.paginationService
                .orderFormat(options.order),
            from: 1
        })
            .skip(options.paging.offset)
            .limit(options.paging.limit);

        const count = await this.SchedulingModel.countDocuments(filter);

        const scheduleIds = schedulingData.map((item) => item._id);
        const [enrolledCount, checkInCount] = await Promise.all([
            this.getEnrolledCount(scheduleIds),
            this.getCheckinCount(scheduleIds)
        ]);

        list = await Promise.all(schedulingData.map(async (item: any) => {
            const populatedSchedule = await item.populate([
                { path: "facilityId", select: "_id facilityName address profilePicture" },
                { path: "trainerId", select: "_id name firstName lastName email" },

                { path: "serviceCategoryId", select: "_id name image attributeType appointmentType" },
                { path: "roomId", select: "_id roomName capacity description" },
            ]);
            const appointmentTypes = populatedSchedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === populatedSchedule.subTypeId?.toString());
            const enrolled = enrolledCount.find((item) => item.schedulingId.toString() === populatedSchedule._id.toString())?.count || 0;
            const checkIns = checkInCount.find((item) => item.schedulingId.toString() === populatedSchedule._id.toString())?.count || 0;

            return {
                _id: populatedSchedule._id,
                organizationId: item.organizationId,
                facility: {
                    _id: populatedSchedule.facilityId._id,
                    facilityName: populatedSchedule.facilityId.facilityName,
                },
                trainer: {
                    _id: populatedSchedule.trainerId._id,
                    name: `${populatedSchedule.trainerId.firstName || ""} ${populatedSchedule.trainerId.lastName || ""}`,
                },
                serviceCategory: {
                    _id: populatedSchedule.serviceCategoryId._id,
                    name: populatedSchedule.serviceCategoryId.name,
                    image: populatedSchedule.serviceCategoryId.image || "",
                },
                subType: {
                    _id: matchedAppointment?._id,
                    name: matchedAppointment?.name,
                    image: matchedAppointment?.image || "",
                },
                room: populatedSchedule.roomId ? {
                    _id: populatedSchedule.roomId._id,
                    name: populatedSchedule.roomId.roomName,
                    capacity: populatedSchedule.roomId.capacity,
                } : null,
                classType: populatedSchedule.classType,
                scheduleStatus: populatedSchedule.scheduleStatus,
                capacity: populatedSchedule.classCapacity,
                enrolled: enrolled,
                checkIns: checkIns,
                date: populatedSchedule.date,
                from: populatedSchedule.from,
                to: populatedSchedule.to,
                duration: populatedSchedule.duration,
                sessions: populatedSchedule.sessions,
            };
        }));

        return {
            data: list,
            count: count
        };
    }

    async getScheduleDetails(scheduleId: IDatabaseObjectId, organizationId?: IDatabaseObjectId, clientId?: IDatabaseObjectId): Promise<any> {
        try {
            const find = {
                _id: scheduleId,
                classType: ClassType.CLASSES,
            }
            if (organizationId) {
                find['organizationId'] = organizationId
            }

            const [schedule, enrolledCount] = await Promise.all([this.SchedulingModel.findOne(find), this.getEnrolledCount([new Types.ObjectId(scheduleId)])]);
            if (!schedule) throw new NotFoundException("Schedule not found");

            const [populatedSchedule, checkIns]: [any, any] = await Promise.all([
                schedule.populate([
                    { path: "facilityId", select: "_id facilityName address profilePicture" },
                    { path: "trainerId", select: "_id name firstName lastName email" },
                    { path: "serviceCategoryId", select: "_id name attributeType appointmentType" },
                    { path: "roomId", select: "_id roomName capacity description" },
                ]),
                this.EnrollmentModel.countDocuments({ schedulingId: scheduleId, isCheckedIn: true })
            ]);

            const appointmentTypes = populatedSchedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === populatedSchedule.subTypeId?.toString());
            const enrollment = clientId ? await this.EnrollmentModel.findOne({ schedulingId: scheduleId, userId: clientId }) : null;

            const data = {
                _id: populatedSchedule._id,
                organizationId: populatedSchedule.organizationId,
                facility: {
                    _id: populatedSchedule.facilityId._id,
                    facilityName: populatedSchedule.facilityId.facilityName,
                },
                trainer: {
                    _id: populatedSchedule.trainerId._id,
                    name: `${populatedSchedule.trainerId.firstName || ""} ${populatedSchedule.trainerId.lastName || ""}`,
                },
                serviceCategory: {
                    _id: populatedSchedule.serviceCategoryId._id,
                    name: populatedSchedule.serviceCategoryId.name,
                },
                subType: {
                    _id: matchedAppointment?._id,
                    name: matchedAppointment?.name,
                },
                room: populatedSchedule.roomId ? {
                    _id: populatedSchedule.roomId._id,
                    name: populatedSchedule.roomId.roomName,
                    capacity: populatedSchedule.roomId.capacity,
                } : null,
                classType: populatedSchedule.classType,
                scheduleStatus: populatedSchedule.scheduleStatus,
                capacity: populatedSchedule.classCapacity,
                dateRange: schedule.dateRange,
                enrolled: enrolledCount[0]?.count || 0,
                date: populatedSchedule.date,
                from: populatedSchedule.from,
                to: populatedSchedule.to,
                checkIns: checkIns,
                duration: populatedSchedule.duration,
                sessions: populatedSchedule.sessions,
                notes: schedule.notes,
                isEnrolled: clientId ? !!enrollment : undefined,
                isCheckedIn: clientId ? (enrollment && enrollment.isCheckedIn) : undefined,
            }
            return data;
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getScheduleDetailsClass(user: Record<string, any>, organizationId: IDatabaseObjectId, scheduleId: IDatabaseObjectId, dto: GetScheduleDetailsDto): Promise<any> {
        try {
            const { role } = user;
            let { dateRange, startDate, endDate, markType } = dto;
            const query = {
                _id: new Types.ObjectId(scheduleId),
                classType: ClassType.CLASSES
            };
            if (!dateRange) {
                dateRange = DateRange.SINGLE;
            }
            switch (role.type) {
                case ENUM_ROLE_TYPE.ORGANIZATION:
                    query['organizationId'] = user._id;
                    break;
                case ENUM_ROLE_TYPE.WEB_MASTER:
                case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                    const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                    if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                    query['organizationId'] = staffDetails.organizationId;
                    break;
                case ENUM_ROLE_TYPE.TRAINER:
                    const trainerDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                    if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                    query["organizationId"] = trainerDetails.organizationId;
                    query["trainerId"] = user._id;
                    break;
                default:
                    throw new BadRequestException("Access denied");
            }

            let baseSchedule: any = await this.SchedulingModel.findOne(query);
            if (!baseSchedule) {
                throw new NotFoundException("Schedule not found");
            }
            if (dateRange === DateRange.MULTIPLE) {
                const recurringQuery: any = {
                    facilityId: new Types.ObjectId(baseSchedule.facilityId),
                    serviceCategoryId: new Types.ObjectId(baseSchedule.serviceCategoryId),
                    subTypeId: new Types.ObjectId(baseSchedule.subTypeId),
                    trainerId: new Types.ObjectId(baseSchedule.trainerId),
                    classType: ClassType.CLASSES,
                    scheduleStatus: baseSchedule.scheduleStatus,
                    from: baseSchedule.from,
                    organizationId: new Types.ObjectId(organizationId),
                    date: { $gte: new Date(startDate) }
                };

                let computedEndDate = endDate;
                if (markType === MarkAvailabilityType.CUSTOM) {
                    if (!endDate) throw new BadRequestException("End date is required for CUSTOM mark type.");
                    const maxAllowed = addDays(new Date(startDate), 6);
                    if (isAfter(endDate, maxAllowed)) computedEndDate = maxAllowed;
                } else if (markType === MarkAvailabilityType.WEEKLY) {
                    computedEndDate = addDays(new Date(startDate), 6);
                }
                recurringQuery["date"]["$lt"] = new Date(computedEndDate);
                const schedules = await this.SchedulingModel.find(recurringQuery).sort({ date: 1 });
                if (!schedules || schedules.length === 0) return [];

                const weekDays: any = {
                    mon: {},
                    tue: {},
                    wed: {},
                    thu: {},
                    fri: {},
                    sat: {},
                    sun: {}
                };

                const seenDays = new Set<string>();
                const schedulesMap = new Map<string, any>();
                schedules.forEach(item => schedulesMap.set(new Date(item.date).toDateString(), item));

                let current = new Date(startDate);
                const end = new Date(computedEndDate);

                while (current <= end) {
                    const dayName = current.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
                    if (!seenDays.has(dayName)) {
                        const item = schedulesMap.get(current.toDateString());
                        if (item) {
                            weekDays[dayName] = {
                                from: item.from,
                                to: item.to,
                                duration: item.duration,
                                classCapacity: item.classCapacity,
                            };
                        }
                        seenDays.add(dayName);
                    }
                    current.setDate(current.getDate() + 1);
                }

                const scheduleId = schedules[0]._id;
                const detail = await this.getScheduleDetails(scheduleId, organizationId); // reuses your single fetch logic
                const {
                    from: _from,
                    to: _to,
                    dateRange: _dateRange,
                    classCapacity: _classCapacity,
                    duration: _duration,
                    notes: _notes,
                    room: _room,
                    enrolled: _enrolled,
                    checkIns: _checkIns,
                    ...restDetail
                } = detail;

                return {
                    ...restDetail,
                    startDate: new Date(startDate),
                    endDate: new Date(endDate),
                    slots: weekDays
                };
            }

            else if (dateRange === DateRange.SINGLE) {
                const enrolledCount = await this.getEnrolledCount([new Types.ObjectId(scheduleId)])

                const [populatedSchedule, checkIns]: [any, any] = await Promise.all([
                    baseSchedule.populate([
                        { path: "facilityId", select: "_id facilityName address profilePicture" },
                        { path: "trainerId", select: "_id name firstName lastName email" },
                        { path: "serviceCategoryId", select: "_id name attributeType appointmentType" },
                        { path: "roomId", select: "_id roomName capacity description" },
                    ]),
                    this.EnrollmentModel.countDocuments({ schedulingId: scheduleId, isCheckedIn: true })
                ]);

                const appointmentTypes = populatedSchedule?.serviceCategoryId?.appointmentType || [];
                const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === populatedSchedule.subTypeId?.toString());
                const enrollment = null;

                const data = {
                    _id: populatedSchedule._id,
                    organizationId: populatedSchedule.organizationId,
                    facility: {
                        _id: populatedSchedule.facilityId._id,
                        facilityName: populatedSchedule.facilityId.facilityName,
                    },
                    trainer: {
                        _id: populatedSchedule.trainerId._id,
                        name: `${populatedSchedule.trainerId.firstName || ""} ${populatedSchedule.trainerId.lastName || ""}`,
                    },
                    serviceCategory: {
                        _id: populatedSchedule.serviceCategoryId._id,
                        name: populatedSchedule.serviceCategoryId.name,
                    },
                    subType: {
                        _id: matchedAppointment?._id,
                        name: matchedAppointment?.name,
                    },
                    room: populatedSchedule.roomId ? {
                        _id: populatedSchedule.roomId._id,
                        name: populatedSchedule.roomId.roomName,
                        capacity: populatedSchedule.roomId.capacity,
                    } : null,
                    classType: populatedSchedule.classType,
                    scheduleStatus: populatedSchedule.scheduleStatus,
                    capacity: populatedSchedule.classCapacity,
                    dateRange: baseSchedule.dateRange,
                    enrolled: enrolledCount[0]?.count || 0,
                    date: populatedSchedule.date,
                    from: populatedSchedule.from,
                    to: populatedSchedule.to,
                    checkIns: checkIns,
                    duration: populatedSchedule.duration,
                    sessions: populatedSchedule.sessions,
                    notes: baseSchedule.notes,
                    isEnrolled: undefined,
                    isCheckedIn: undefined,
                }
                return data;
            }

        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getCustomerList(organizationId: IDatabaseObjectId, find: ClassScheduleGetUserForEnrollmentDto & { [key: string]: any }, options: IDatabaseFindAllOptions): Promise<any> {
        try {
            const { scheduleId, _search } = find
            const schedule = await this.SchedulingModel.findById(scheduleId)
            if (!schedule) {
                throw new NotFoundException("Schedule not found")
            }
            const { facilityId, classType, serviceCategoryId, subTypeId, date, from } = schedule

            let userMatch: any = {}
            if (_search) {
                userMatch['$match'] = {
                    ...userMatch,
                    ..._search
                }
            }

            const usersPipe: PipelineStage[] = [
                {
                    $match: {
                        organizationId,
                        facilityId: facilityId,
                        isExpired: false,
                        startDate: { $lte: moment(date).startOf('day').add(moment.duration(from)).toDate() },
                        endDate: { $gte: moment(date).startOf('day').add(moment.duration(from)).toDate() },
                    }
                },
                {
                    $lookup: {
                        from: "pricings",
                        localField: "packageId",
                        foreignField: "_id",
                        as: "packageData",
                        pipeline: [
                            {
                                $match: {
                                    "services.type": classType,
                                    $or: [
                                        {
                                            $and: [
                                                { "services.serviceCategory": serviceCategoryId },
                                                { "services.appointmentType": subTypeId }
                                            ]
                                        },
                                        {
                                            $and: [
                                                { "services.relationShip.serviceCategory": serviceCategoryId },
                                                { "services.relationShip.subTypeIds": subTypeId }
                                            ]
                                        },
                                    ]
                                },
                            },
                            { $project: { _id: 1 } },
                        ],
                    }
                },
                { $unwind: { path: "$packageData", preserveNullAndEmptyArrays: false } },
                { $unwind: { path: "$consumers", preserveNullAndEmptyArrays: false } },
                {
                    $group: {
                        _id: "$consumers",
                        userId: { $first: "$consumers" }
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                        pipeline: [
                            ..._search ? [
                                userMatch
                            ] : [],
                            {
                                $lookup: {
                                    from: "clients",
                                    localField: "_id",
                                    foreignField: "userId",
                                    as: "clientData",
                                    pipeline: [
                                        { $project: { clientId: 1 } },
                                    ],
                                }
                            },
                            { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: false } },
                            {
                                $project: {
                                    clientId: "$clientData.clientId",
                                    name: 1,
                                    firstName: 1,
                                    lastName: 1,
                                    email: 1,
                                    countryCode: 1,
                                    mobile: 1,
                                },
                            },
                        ],
                    }
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: false } },
                {
                    $lookup: {
                        from: "enrollments",
                        let: {
                            userId: "$userId",
                            schedulingId: new Types.ObjectId(scheduleId)
                        },
                        pipeline: [{
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$userId", "$$userId"] },
                                        { $eq: ["$schedulingId", "$$schedulingId"] }
                                    ]
                                }
                            }
                        }],
                        as: "enrollmentData",
                    },
                },
                { $unwind: { path: "$enrollmentData", preserveNullAndEmptyArrays: true } },
                { $match: { "enrollmentData._id": { $exists: false } } },
                {
                    $facet: {
                        total: [
                            { $count: "total" }
                        ],
                        list: [
                            { $sort: this.paginationService.orderFormat(options.order) },
                            { $skip: options.paging.offset },
                            { $limit: options.paging.limit },
                            {
                                $project: {
                                    _id: "$userId",
                                    clientId: "$userData.clientId",
                                    name: "$userData.name",
                                    firstName: "$userData.firstName",
                                    lastName: "$userData.lastName",
                                    email: { $cond: { if: "$userData.email", then: "$userData.email", else: "" } },
                                    countryCode: { $cond: { if: "$userData.countryCode", then: "$userData.countryCode", else: "" } },
                                    mobile: { $cond: { if: "$userData.mobile", then: "$userData.mobile", else: "" } },
                                    enrollmentId: { $cond: { if: "$enrollmentData._id", then: "$enrollmentData._id", else: null } },
                                    isEnrolled: { $cond: { if: "$enrollmentData._id", then: true, else: false } },
                                    isCheckedIn: { $cond: { if: "$enrollmentData.isCheckedIn", then: "$enrollmentData.isCheckedIn", else: false } },
                                    enrolledDate: { $cond: { if: "$enrollmentData.createdAt", then: "$enrollmentData.createdAt", else: null } },
                                },
                            },
                        ],
                    }
                }
            ]

            const result = await this.PurchaseModel.aggregate(usersPipe);
            return {
                list: result[0]?.list || [],
                count: result[0]?.total[0]?.total || 0,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getParticipantList(find: ClassScheduleGetUserForEnrollmentDto & { [key: string]: any }, options: IDatabaseFindAllOptions): Promise<any> {
        try {
            const { scheduleId, _search } = find
            const schedule = await this.SchedulingModel.exists({ _id: scheduleId })
            if (!schedule) {
                throw new NotFoundException("Schedule not found")
            }

            let userMatch: any = {}
            if (_search) {
                userMatch['$match'] = {
                    ...userMatch,
                    ..._search
                }
            }

            const pipeline: PipelineStage[] = [
                {
                    $match: {
                        schedulingId: new Types.ObjectId(scheduleId)
                    }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                        pipeline: [
                            ..._search ? [
                                userMatch
                            ] : [],
                            {
                                $lookup: {
                                    from: "clients",
                                    localField: "_id",
                                    foreignField: "userId",
                                    as: "clientData",
                                    pipeline: [
                                        { $project: { clientId: 1 } },
                                        { $limit: 1 }
                                    ],
                                }
                            },
                            { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: false } },
                            {
                                $project: {
                                    clientId: "$clientData.clientId",
                                    name: 1,
                                    firstName: 1,
                                    lastName: 1,
                                    email: 1,
                                    countryCode: 1,
                                    mobile: 1,
                                },
                            },
                            { $limit: 1 }
                        ],
                    }
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: false } },
                {
                    $facet: {
                        total: [
                            { $count: "total" }
                        ],
                        list: [
                            { $sort: this.paginationService.orderFormat(options.order) },
                            { $skip: options.paging.offset },
                            { $limit: options.paging.limit },
                            {
                                $project: {
                                    _id: "$userId",
                                    clientId: "$userData.clientId",
                                    name: "$userData.name",
                                    firstName: "$userData.firstName",
                                    lastName: "$userData.lastName",
                                    email: { $cond: { if: "$userData.email", then: "$userData.email", else: "" } },
                                    countryCode: { $cond: { if: "$userData.countryCode", then: "$userData.countryCode", else: "" } },
                                    mobile: { $cond: { if: "$userData.mobile", then: "$userData.mobile", else: "" } },
                                    enrollmentId: { $cond: { if: "$_id", then: "$_id", else: null } },
                                    isEnrolled: { $cond: { if: "$_id", then: true, else: false } },
                                    isCheckedIn: { $cond: { if: "$isCheckedIn", then: "$isCheckedIn", else: false } },
                                    enrolledDate: { $cond: { if: "$createdAt", then: "$createdAt", else: null } },
                                },
                            },
                        ],
                    }
                }
            ]

            const result = await this.EnrollmentModel.aggregate(pipeline);
            return {
                list: result[0]?.list || [],
                count: result[0]?.total[0]?.total || 0,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async cancelSchedule(user: IUserDocument, scheduleId: IDatabaseObjectId, dto: CancelRecurringScheduleDto): Promise<boolean> {
        const session = await this.transactionService.startTransaction();

        try {
            let { startDate, endDate, dateRange } = dto;
            if (!dateRange) {
                dateRange = DateRange.SINGLE;
            }

            const baseQuery = { _id: scheduleId, classType: ClassType.CLASSES };
            const { role } = user;


            switch (role.type) {
                case ENUM_ROLE_TYPE.ORGANIZATION:
                    baseQuery['organizationId'] = user._id;
                    break;
                case ENUM_ROLE_TYPE.WEB_MASTER:
                case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN: {
                    const staffDetails = await this.StaffProfileModel.findOne(
                        { userId: user._id },
                        { organizationId: 1 }
                    );
                    if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                    baseQuery['organizationId'] = staffDetails.organizationId;
                    break;
                }
                case ENUM_ROLE_TYPE.TRAINER: {
                    const trainerDetails = await this.StaffProfileModel.findOne(
                        { userId: user._id },
                        { organizationId: 1 }
                    );
                    if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                    baseQuery["organizationId"] = trainerDetails.organizationId;
                    baseQuery["trainerId"] = user._id;
                    break;
                }
                default:
                    throw new BadRequestException("Access denied");
            }
            const schedule = await this.SchedulingModel.findOne(baseQuery).session(session);
            if (!schedule) throw new NotFoundException("Schedule not found");

            const istNow = DateTime.now().setZone("Asia/Kolkata");
            const istToday = new Date(istNow);
            istToday.setHours(0, 0, 0, 0);
            istToday.setMinutes(istToday.getMinutes() + 330);

            const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");
            const scheduleDateStr = schedule.date.toISOString().split("T")[0];
            const todayStr = istToday.toISOString().split("T")[0];

            if (currentISTTime >= schedule.to && scheduleDateStr === todayStr) {
                throw new BadRequestException(`You cannot cancel past schedules for ${moment(schedule.date).format("YYYY-MM-DD")}`);
            }

            const isMultiple = dateRange === DateRange.MULTIPLE;
            if (isMultiple) {
                const baseScheduleDate = new Date(schedule.date);
                if (!startDate || !endDate) throw new BadRequestException("Start and End Date are required for multiple cancellation");
                if (startDate > endDate) throw new BadRequestException("Start date must be before or equal to End date");

                if (!(startDate >= istToday)) {
                    throw new BadRequestException("Start date should greater than or equal to current Date");
                }

                // if (baseScheduleDate.toISOString().split("T")[0] !== new Date(startDate).toISOString().split("T")[0]) {
                //     throw new BadRequestException("Start date should be same as the base schedule date");
                // }

                if (schedule.classType !== ClassType.CLASSES) {
                    throw new BadRequestException("Only personal appointment can be cancelled");
                }

                const { facilityId, trainerId, serviceCategoryId, subTypeId, organizationId, from } = schedule;

                const recurringQuery: any = {
                    facilityId,
                    trainerId,
                    serviceCategoryId,
                    subTypeId,
                    organizationId,
                    from,
                    classType: ClassType.CLASSES,
                    scheduleStatus: ScheduleStatusType.BOOKED,
                    date: {
                        $gte: new Date(startDate),
                        $lt: new Date(endDate)
                    }
                };

                const schedules = await this.SchedulingModel.find(recurringQuery).session(session);
                if (!schedules.length) {
                    throw new NotFoundException("No matching recurring schedules found.");
                }

                const scheduleIds = schedules.map(s => s._id);

                await this.SchedulingModel.updateMany(
                    { _id: { $in: scheduleIds } },
                    {
                        $set: {
                            scheduleStatus: ScheduleStatusType.CANCELED,
                            canceledBy: user._id,
                            canceledAt: new Date(),
                        }
                    },
                    { session }
                );

                await this.EnrollmentModel.updateMany(
                    { schedulingId: { $in: scheduleIds } },
                    {
                        $set: {
                            isCheckedIn: false,
                            enrollmentStatus: ScheduleStatusType.CANCELED,
                        }
                    },
                    { session }
                );

                // const enrollments = await this.EnrollmentModel.find(
                //     { schedulingId: { $in: scheduleIds } },
                //     null,
                //     { session }
                // );

                const purchaseSessionsMap = new Map<string, number>();
                for (const schedule of schedules) {
                    const key = schedule.purchaseId?.toString();
                    if (key) {
                        const count = purchaseSessionsMap.get(key) || 0;
                        purchaseSessionsMap.set(key, count + (schedule.sessions || 1));
                    }
                }

                const refundPromises = Array.from(purchaseSessionsMap.entries()).map(
                    ([purchaseId, sessionCount]) =>
                        this.PurchaseModel.updateOne(
                            { _id: new Types.ObjectId(purchaseId) },
                            { $inc: { sessionConsumed: -1 * sessionCount } },
                            { session }
                        )
                );

                await Promise.all(refundPromises);



            }
            else if (dateRange === DateRange.SINGLE) {
                const now = moment()
                const scheduleDate = moment(`${schedule.date.toISOString().split("T")[0]}T${schedule.from}:00`);
                if (scheduleDate < now) throw new BadRequestException("Schedule already passed");
                if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException("Scheduled class is already cancelled");

                schedule.scheduleStatus = ScheduleStatusType.CANCELED;
                schedule.canceledBy = user._id;
                schedule.canceledAt = new Date();

                const enrollments = await this.EnrollmentModel.find({ schedulingId: scheduleId });
                const purchaseIds = enrollments.map((enrollment) => enrollment.purchaseId);
                await Promise.all([
                    schedule.save({ session }),
                    this.EnrollmentModel.updateMany({ schedulingId: scheduleId }, { $set: { isCheckedIn: false, enrollmentStatus: ScheduleStatusType.CANCELED } }, { session }),
                    this.PurchaseModel.updateMany({ _id: { $in: purchaseIds } }, { $inc: { sessionConsumed: -1 * schedule.sessions } }, { session })
                ])
            }

            await this.transactionService.commitTransaction(session);
            session.endSession();
            this.waitTimeGatewayService.sendWaitingTimeUpdate(schedule.facilityId);
            return true
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async checkForExistingScheduleConflictV1(date: Date, from: string, to: string, scheduleId?: string) {
        const existingBooking = await this.SchedulingModel.find({
            _id: { $ne: scheduleId },
            classType: { $in: [ClassType.CLASSES] },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }, // skip canceled
            date,
        });

        for (let booking of existingBooking) {
            // Check for overlapping time slots
            const existingFrom = booking.from;
            const existingTo = booking?.to;

            if (
                existingBooking &&
                ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                    (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                    (from <= existingFrom && to >= existingTo))
            ) {
                // New booking completely overlaps existing booking
                throw new BadRequestException(`Schedule already exists from ${existingFrom} to ${existingTo}`);
            }
        }
        return true;
    }

    async checkForExistingScheduleConflict(startDate: Date, endDate: Date, requestedSchedule: IScheduleDates[], scheduleIds?: string[] | IDatabaseObjectId[]) {
        const existingBooking = await this.SchedulingModel.aggregate([
            {
                $match: {
                    _id: { $nin: scheduleIds ?? [] },
                    classType: { $in: [ClassType.CLASSES] },
                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }, // skip canceled
                    date: {
                        $gte: moment(startDate).utc(true).toDate(),
                        $lte: moment(endDate).utc(true).toDate(),
                    }
                }
            },
            {
                $group: {
                    _id: {
                        date: "$date",
                        from: "$from",
                    },
                    from: { $first: "$from" },
                    to: { $first: "$to" },
                },
            },
            {
                $project: {
                    _id: 0,
                    from: 1,
                    to: 1,
                    date: { $dateToString: { format: "%Y-%m-%d", date: "$_id.date" } },
                },
            },
            { $sort: { date: 1, from: 1 } },
        ]);

        const requestedScheduleMap = new Map<string, IScheduleDates[]>();
        for (const slot of requestedSchedule) {
            const date = slot.date;
            console.log("requested", moment(date).utc(true).startOf('day').toDate().toISOString())
            const day = slot.day;
            if (!requestedScheduleMap.has(moment(date).utc(true).startOf('day').toDate().toISOString())) {
                requestedScheduleMap.set(moment(date).utc(true).startOf('day').toDate().toISOString(), []);
            }
            requestedScheduleMap.get(moment(date).utc(true).startOf('day').toDate().toISOString())!.push(slot);
        }
        const existingBookingMap = new Map<string, Omit<IScheduleDates, "day">[]>();
        for (const booking of existingBooking) {
            const date = booking.date;
            console.log("existing", moment(date).utc(true).startOf('day').toDate().toISOString())
            if (!existingBookingMap.has(moment(date).utc(true).startOf('day').toDate().toISOString())) {
                existingBookingMap.set(moment(date).utc(true).startOf('day').toDate().toISOString(), []);
            }
            existingBookingMap.get(moment(date).utc(true).startOf('day').toDate().toISOString())!.push(booking);
        }

        for (const [date, slots] of requestedScheduleMap) {
            const requestedSlots = slots;
            for (const requestedSlot of requestedSlots) {
                const from = requestedSlot.from;
                const to = requestedSlot.to;

                const existingBookingsForDate = existingBookingMap.get(moment(date).utc(true).startOf('day').toDate().toISOString()) || [];
                for (const existingBooking of existingBookingsForDate) {
                    const existingFrom = existingBooking.from;
                    const existingTo = existingBooking.to;

                    if (
                        existingBooking &&
                        ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                            (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                            (from <= existingFrom && to >= existingTo))
                    ) {
                        // New booking completely overlaps existing booking
                        throw new BadRequestException(`Schedule already exists from ${existingFrom} to ${existingTo} on ${moment(date).format("DD MMM")}`);
                    }
                }
            }
        }
        return true;
    }

    /**
     * Get enrollment count for a list of schedules
     * @param scheduleIds 
     * @returns 
     */
    async getEnrollmentCount(scheduleIds: IDatabaseObjectId[]): Promise<{ schedulingId: IDatabaseObjectId; count: number }[]> {
        return this.EnrollmentModel.aggregate([
            { $match: { schedulingId: { $in: scheduleIds } } },
            { $group: { _id: "$schedulingId", count: { $sum: 1 } } },
            { $project: { _id: 0, schedulingId: "$_id", count: 1 } },
        ]);
    }

    private isFromLessThanTo({ from, to }: { from: string; to: string }): boolean {
        return new Date(`1970-01-01T${from}:00`) < new Date(`1970-01-01T${to}:00`);
    }

    /**
     * Schedule a class
     * @param organizationId 
     * @param user 
     * @param body 
     * @returns 
     */
    async createClassScheduling(organizationId: IDatabaseObjectId, user: IUserDocument, body: ClassScheduleCreateDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        const maxYearToMark = 1;
        try {
            const { facilityId, trainerId, subType, serviceCategory, dateRange, markType, schedule, notes, capacity } = body;
            let { startDate, endDate } = body
            let { roomId }: { roomId?: IDatabaseObjectId } = body as any;
            let availableRooms = new Map<number, Map<string, IDatabaseObjectId[]>>();
            if (markType === MarkAvailabilityType.CUSTOM && !endDate) {
                throw new BadRequestException("End date is required for custom date range");
            }
            if (markType === MarkAvailabilityType.WEEKLY) {
                endDate = new Date(startDate);
                endDate.setFullYear(endDate.getFullYear() + maxYearToMark);
            }

            if (moment(endDate) > moment(startDate).add(maxYearToMark, 'year')) throw new BadRequestException(`Class cannot scheduled more than ${maxYearToMark} year from now.`);

            const facility = await this.FacilityModel.findOne({ _id: facilityId, organizationId }).exec();
            await this.schedulingService.validateFacilityOwnership(facility, user);
            await this.schedulingService.validateFacilityAvailability(startDate, endDate, schedule, facilityId);

            const service = await this.ServiceModel.findOne({ _id: serviceCategory, organizationId, classType: ClassType.CLASSES, "appointmentType._id": subType });
            if (!service) throw new BadRequestException("Invalid service selected.");
            const appointmentType = service.appointmentType.find((item: AppointmentTypeDocument) => item._id.toString() === subType);
            if (!appointmentType) throw new BadRequestException("Invalid service selected.");
            const appointmentDuration = appointmentType.durationInMinutes as number;

            const scheduleDateList = await this.schedulingService.buildScheduleDates(startDate, endDate, schedule, appointmentDuration);
            if (!scheduleDateList.length) throw new BadRequestException("No schedule dates found");
            if (roomId) {
                const room = await this.RoomModel.findOne({ _id: roomId, facilityId }).exec();
                await this.schedulingService.validateRoomAvailability(room, startDate, endDate, scheduleDateList);
            } else {
                availableRooms = await this.schedulingService.getAvailableRoomsMultiple(facilityId, ClassType.CLASSES, serviceCategory, scheduleDateList, [], session);
                if (!availableRooms.size) throw new BadRequestException("No available rooms found");
            }

            await this.checkForExistingScheduleConflict(startDate, endDate, scheduleDateList);
            await this.schedulingService.validateStaffSpecialization(trainerId, serviceCategory, subType);

            const newSchedules = scheduleDateList.map((item: IScheduleDates) => {
                const date = item.date;
                const from = item.from;
                const to = item.to;
                const duration = item.duration;
                const requestedSessions = item.sessions;
                const capacity = item?.capacity;

                const room = roomId ?? availableRooms.get(moment(date).utc(true).startOf('day').toDate().getTime())?.get(from)?.[0];
                if (!room) throw new BadRequestException(`No available rooms found for ${moment(date).format("DD  MMM")} from ${from} to ${to}`);

                return new this.SchedulingModel({
                    organizationId,
                    scheduledBy: user._id,
                    facilityId: facilityId,
                    classType: ClassType.CLASSES,
                    subTypeId: subType,
                    trainerId: trainerId,
                    serviceCategoryId: serviceCategory,
                    roomId: room ?? null,
                    dateRange: dateRange,
                    date: moment(date).utc(true).startOf('day').toDate(),
                    from: from,
                    to: to,
                    duration: duration,
                    sessions: requestedSessions,
                    notes: notes,
                    classCapacity: capacity,
                });
            });

            const data = await this.SchedulingModel.insertMany(newSchedules, { session });

            await this.transactionService.commitTransaction(session);
            // await this.sendClassScheduleCreationEmail(newSchedule, user);
            return {
                total: data.length,
                data: data.map((item: any) => item._id)
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updateClassSchedulingV1(organizationId: any, user: any, body: ClassScheduleUpdateDtoV1): Promise<any> {
        const { scheduleId, facilityId, trainerId, classType, subType, serviceCategory, duration, roomId, dateRange, date, from, to, notes, capacity } = body;
        const schedule = await this.SchedulingModel.findOne({ _id: scheduleId, organizationId }).exec();
        if (!schedule) throw new NotFoundException("Schedule not found");
        if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException("Scheduled class is already cancelled");

        const sessionStartDate = new Date(schedule.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > schedule.to)) {
            throw new BadRequestException("You can only update it before or at the scheduled start time");
        }

        if (!this.isFromLessThanTo({ from: body.from, to: body.to })) throw new BadRequestException("Invalid time range.");
        if (new Date(`${date.toISOString().split("T")[0]}T${from}:00`) < new Date()) throw new BadRequestException("Class cannot start in the past.");
        const facility = await this.FacilityModel.findOne({ _id: facilityId, organizationId }).exec();
        await this.schedulingService.validateFacilityOwnership(facility, user);
        await this.schedulingService.validateFacilityAvailabilityV1(date, { from, to }, facilityId);
        await this.checkForExistingScheduleConflictV1(date, from, to, scheduleId);
        await this.schedulingService.validateStaffSpecialization(trainerId, serviceCategory, subType);
        await this.schedulingService.validateIsStaffBusy(trainerId, date, from, to, scheduleId);
        if (roomId) await this.schedulingService.validateRoomAvailabilityV1(roomId, date, from, to, scheduleId);
        const enrollmentCount = await this.getEnrollmentCount([new Types.ObjectId(scheduleId)]);
        if (enrollmentCount?.length && enrollmentCount[0]?.count) {
            throw new BadRequestException("Cannot update schedule. Some user has already enrolled in this class.");
        };

        const service = await this.ServiceModel.findOne({ _id: serviceCategory, organizationId, classType, "appointmentType._id": subType });
        if (!service) throw new BadRequestException("Invalid service selected.");
        const appointmentType = service.appointmentType.find((item: AppointmentTypeDocument) => item._id.toString() === subType);
        if (!appointmentType) throw new BadRequestException("Invalid service selected.");
        const appointmentDuration = appointmentType.durationInMinutes as number;
        const requestedSessions = duration / (appointmentDuration || 1);
        const remainder = duration % (appointmentDuration || 1);
        if (remainder !== 0) {
            throw new BadRequestException("Invalid duration.");
        };

        schedule.facilityId = facilityId;
        schedule.classType = classType;
        schedule.subTypeId = new Types.ObjectId(subType);
        schedule.trainerId = trainerId;
        schedule.serviceCategoryId = new Types.ObjectId(serviceCategory);
        schedule.roomId = new Types.ObjectId(roomId);
        schedule.roomId = new Types.ObjectId(roomId);
        schedule.roomId = new Types.ObjectId(roomId);
        schedule.dateRange = dateRange;
        schedule.date = new Date(date);
        schedule.from = from;
        schedule.to = to;
        schedule.duration = duration;
        schedule.sessions = requestedSessions;
        schedule.notes = notes;
        schedule.classCapacity = capacity;

        await schedule.save();

        return schedule;
    }

    /**
     * Under construction
     * @param organizationId 
     * @param user 
     * @param body 
     */
    async updateClassScheduling(organizationId: IDatabaseObjectId, user: IUserDocument, body: ClassScheduleUpdateDto): Promise<any> {
        const { scheduleIds, facilityId, trainerId, classType, subType, serviceCategory, dateRange, startDate, endDate, from, to, schedule, notes, capacity } = body;
        const session = await this.transactionService.startTransaction();
        try {
            const deleteSchedule = []
            const updateSchedule = []

            const filter: Record<string, any> = {
                organizationId,
                scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }
            };

            if (scheduleIds?.length) {
                filter["_id"] = { $in: scheduleIds }
            } else {
                filter["date"] = { $gte: startDate, $lte: endDate };
                filter["from"] = from;
                filter["to"] = to;
            }

            const scheduleDateList = await this.schedulingService.buildScheduleDates(startDate, endDate, schedule);
            if (!scheduleDateList.length) throw new BadRequestException("No schedule dates found");


            const schedules = await this.SchedulingModel.find(filter).exec();
            if (!schedules.length) throw new NotFoundException("Schedule(s) not found");

            const existingBookingMap = new Map<string, Omit<IScheduleDates, "day">[]>();
            for (const booking of schedules) {
                const date = booking.date;
                console.log("existing", moment(date).utc(true).toISOString())
                if (!existingBookingMap.has(moment(date).utc(true).toISOString())) {
                    existingBookingMap.set(moment(date).utc(true).toISOString(), []);
                }
                existingBookingMap.get(moment(date).utc(true).toISOString())!.push({
                    from: booking.from,
                    to: booking.to,
                    date: booking.date,
                    duration: booking.duration,
                    sessions: booking.sessions,
                });
            }

            const scheduleDateSet = new Set(scheduleDateList.map((item: any) => `${moment(item.date).utc(true).toISOString()}-${item.from}-${item.to}`));

            // filter schedule to update and delete form schedules and scheduleDateList
            schedules.forEach((item: any) => {
                const scheduleDate = scheduleDateSet.has(`${moment(item.date).utc(true).toISOString()}-${item.from}-${item.to}`);
                if (scheduleDate) {
                    updateSchedule.push(item._id);
                } else {
                    deleteSchedule.push(item._id);
                }
            });

            const toUpdateIds: IDatabaseObjectId[] = updateSchedule.map((item: any) => item._id);
            await this.checkForExistingScheduleConflict(startDate, endDate, scheduleDateList, toUpdateIds);
            const enrollmentCount = await this.getEnrollmentCount(toUpdateIds);
            const enrollmentMap = new Map(enrollmentCount.map((item: any) => [item.schedulingId, item.count]));

            if (deleteSchedule.length) {
                await this.SchedulingModel.deleteMany({ _id: { $in: deleteSchedule } }, { session });
            }

            // check for existing schedule conflict


        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }

    }

    async deleteSchedule(organizationId: IDatabaseObjectId, schedulingId: IDatabaseObjectId): Promise<any> {
        try {
            const schedule = await this.SchedulingModel.findOne({ _id: schedulingId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            if (schedule.organizationId?.toString() !== organizationId.toString()) throw new NotFoundException(`Schedule does not belong to the current organization`);

            const sessionStartDateTime = moment(schedule.date).format("YYYY-MM-DD") + " " + schedule.from;
            const currentDateTime = moment();

            if (currentDateTime.isAfter(moment(sessionStartDateTime, "YYYY-MM-DD HH:mm"))) throw new BadRequestException("You can only delete the session before it starts");

            const isEnrolled = await this.EnrollmentModel.exists({ schedulingId });
            if (isEnrolled) throw new BadRequestException("Schedule cannot be deleted because clients are already enrolled.");

            await this.SchedulingModel.deleteOne({ _id: schedulingId, organizationId });

            return true
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async enrollScheduling(organizationId: IDatabaseObjectId, enrollSchedulingDto: ClassScheduleEnrollDto, isClient?: boolean): Promise<any> {
        const session = await this.transactionService.startTransaction();
        console.log("enrollSchedulingDto", organizationId);
        try {
            const { scheduleId, users } = enrollSchedulingDto;

            const [schedule, clients, existingEnrollments, totalEnrollments]: [Scheduling, (Omit<Clients, 'userId'> & { userId: UserDocument })[], Enrollment[], number] = await Promise.all([
                this.SchedulingModel.findOne({ _id: scheduleId, organizationId }).lean(),
                this.ClientsModel.find({ userId: { $in: users }, organizationId })
                    .populate<Omit<Clients, 'userId'> & { userId: UserDocument }>("userId")
                    .lean(),
                this.EnrollmentModel.find({ schedulingId: scheduleId, userId: { $in: users } }).lean(),
                this.EnrollmentModel.countDocuments({ schedulingId: scheduleId })
            ]);

            if (!schedule) throw new NotFoundException(`Scheduled class not found`);
            const { date, from, to, sessions: scheduleSession, scheduleStatus, classCapacity, serviceCategoryId, subTypeId } = schedule;

            const now = moment();
            const scheduleDate = date ? moment(date).format("DD/MM/YYYY") : "Unknown Date";
            const scheduleTime = from && to ? `Time: ${from} to ${to}` : "Unknown Time";
            const sessionEnd = date && to ? moment(`${moment(date).format("YYYY-MM-DD")} ${to}`, "YYYY-MM-DD HH:mm") : null;

            // Schedule validation
            if (scheduleStatus === ScheduleStatusType.CANCELED) {
                throw new BadRequestException(`${scheduleDate} ${scheduleTime} is canceled.`);
            }

            if (sessionEnd && now.isAfter(sessionEnd)) {
                throw new BadRequestException(`${scheduleDate} ${scheduleTime} has already ended.`);
            }

            if ((classCapacity - totalEnrollments) < clients.length) {
                throw new BadRequestException(`${scheduleDate} ${scheduleTime} does not have enough spots.${(classCapacity - totalEnrollments) > 0 ? `\nOnly ${(classCapacity - totalEnrollments)} spots are available` : ""}`);;
            }

            const errorMessages: string[] = [];
            const clientMap = new Map(clients.map((c) => [c.userId?._id.toString(), c]));
            const existingEnrollmentSet = new Set(existingEnrollments.map((e) => `${e.schedulingId}-${e.userId}`));
            const missingClients = users.filter((id) => !clientMap.has(id.toString()));
            if (missingClients.length) errorMessages.push(`Clients not found: ${missingClients.join(", ")}`);

            const purchasePipe: PipelineStage[] = [
                {
                    $match: {
                        consumers: { $in: clients.map((client) => client.userId?._id ? new Types.ObjectId(client.userId?._id) : null).filter(Boolean) },
                        isExpired: false,
                        isActive: true,
                        startDate: { $lte: moment(`${date.toISOString().split("T")[0]}T${from}:00`).tz("Asia/Kolkata").toDate() },
                        endDate: { $gte: moment(`${date.toISOString().split("T")[0]}T${from}:00`).tz("Asia/Kolkata").toDate() },
                    }
                },
                {
                    $lookup: {
                        from: "pricings",
                        localField: "packageId",
                        foreignField: "_id",
                        as: "packageData",
                        pipeline: [
                            {
                                $match: {
                                    "services.type": ClassType.CLASSES,
                                    $or: [
                                        {
                                            $and: [
                                                { "services.serviceCategory": serviceCategoryId },
                                                { "services.appointmentType": subTypeId }
                                            ]
                                        },
                                        {
                                            $and: [
                                                { "services.relationShip.serviceCategory": serviceCategoryId },
                                                { "services.relationShip.subTypeIds": subTypeId }
                                            ]
                                        },
                                    ]
                                },
                            },
                        ],
                    },
                },
                {
                    $match: {
                        packageData: { $ne: [] }
                    }
                },
            ];

            const packageData = await this.PurchaseModel.aggregate<Purchase & { packageData: Pricing[] }>(purchasePipe);
            const packageMap: Map<string, any[]> = new Map();
            packageData.forEach((c: Purchase & { packageData: Pricing[] }) => {
                packageMap.set(c.userId.toString(), [c, ...packageMap.get(c.userId.toString()) ?? []]);
            });

            const promises: Promise<any>[] = [];
            for (const client of clients) {
                if (!client) continue;
                const clientName = client.userId?.name || "Unknown Client";
                const userId = client.userId._id;

                if (existingEnrollmentSet.has(`${schedule._id.toString()}-${userId.toString()}`)) {
                    errorMessages.push(`${clientName} is already enrolled in ${moment(schedule.date).format("DD/MM/YYYY")} (${schedule.from} - ${schedule.to}).`);
                    continue;
                }

                const purchaseData = packageMap.get(userId.toString());
                if (!purchaseData || !purchaseData?.length) {
                    errorMessages.push(isClient ? `You don't have an active package for this class` : `${clientName} has no active package for this class`);
                    continue;
                }
                let validPurchase: PurchaseDocument = null;
                let errorMessage = "";
                for (const purchase of purchaseData) {
                    try {
                        const pricing = purchase.packageData[0];
                        validPurchase = await this.schedulingService.validatePackageEligibility(
                            userId.toString(),
                            date,
                            from,
                            to,
                            pricing,
                            purchase,
                            1,
                        )
                    } catch (error) {
                        errorMessage = error.message;
                        continue;
                    }
                    break;
                }
                if (!validPurchase) {
                    errorMessages.push(!isClient ?
                        `${clientName} has no access to this course. ${errorMessage}`
                        : `You don't have access to this class.`
                    );
                    continue;
                };

                const enrollment = new this.EnrollmentModel({
                    schedulingId: schedule._id,
                    userId: client.userId?._id,
                    packageId: validPurchase.packageId,
                    purchaseId: validPurchase._id,
                });
                await enrollment.save({ session });
                await this.PurchaseModel.updateOne({ _id: validPurchase._id }, { $inc: { sessionConsumed: scheduleSession } }, { session });
            }

            if (errorMessages.length) throw new BadRequestException(errorMessages.join("\n"));
            // await Promise.all(promises);
            await session.commitTransaction();
            await this.sendClassEnrollmentConfirmationEmail(clients, schedule);

            return true;
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async cancelEnrollScheduling(organizationId: IDatabaseObjectId, enrollSchedulingDto: ClassCancelEnrollDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const { scheduleId, users } = enrollSchedulingDto;

            const [schedule, enrollments]: [Scheduling, Enrollment[]] = await Promise.all([
                this.SchedulingModel.findOne({ _id: scheduleId, organizationId }).lean(),
                this.EnrollmentModel.find({ schedulingId: scheduleId, userId: { $in: users } }).lean(),
            ]);

            if (!schedule) throw new NotFoundException(`Scheduled class not found`);

            for (const enrollment of enrollments) {
                await this.EnrollmentModel.deleteOne({ _id: enrollment._id }, { session });
                await this.PurchaseModel.updateOne({ _id: enrollment.purchaseId }, { $inc: { sessionConsumed: -1 * schedule.sessions } }, { session });
            }

            // await Promise.all(promises);
            await this.transactionService.commitTransaction(session);
            return true;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async checkIn(organizationId: Types.ObjectId, checkInDto: CheckedInDto, userId?: Types.ObjectId): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const { enrollmentId, isCheckedIn } = checkInDto;
            const enrollment = await this.EnrollmentModel.findOne({ _id: enrollmentId });
            if (!enrollment) throw new BadRequestException("Enrollment not found.");

            if (isCheckedIn && enrollment.isCheckedIn) throw new BadRequestException(`${userId ? "User has" : "You have"} already checked in.`);
            else if (!isCheckedIn && !enrollment.isCheckedIn) throw new BadRequestException(`${userId ? "User has" : "You have"} not arrived.`);

            const schedule = await this.SchedulingModel.findOne({ _id: enrollment.schedulingId, organizationId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);

            const purchase = await this.PurchaseModel.findOne({ _id: enrollment.purchaseId }).session(session);
            if (!purchase) throw new BadRequestException("Purchase not found.");

            if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException("Scheduled class is already cancelled");

            enrollment.isCheckedIn = isCheckedIn;
            enrollment.checkedInDate = isCheckedIn ? new Date() : null;
            await enrollment.save({ session });

            await session.commitTransaction();
            return true;
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }
    private async sendClassScheduleCreationEmail(schedule: any, scheduledBy: any): Promise<void> {
        const serviceData = await this.ServiceModel.findOne({ _id: schedule.serviceCategoryId }).lean();
        const subTypeData = serviceData?.appointmentType?.find((t: any) => t._id.toString() === schedule.subTypeId.toString());
        console.log(serviceData, "service Data")
        const trainerData = await this.UserModel.findOne({ _id: schedule.trainerId }).lean();
        const organizationDetail = await this.UserModel.findOne({ _id: schedule.organizationId }).lean();
        const staffList = await this.getALltheStaffs(new Types.ObjectId(schedule.facilityId));

        const context = {
            clientName: scheduledBy?.name || "Admin",
            className: serviceData?.name || "Class",
            date: schedule.date,
            from: schedule.from,
            to: schedule.to,
            trainerName: trainerData ? `${trainerData.firstName} ${trainerData.lastName}` : "Unknown Trainer",
        };

        // 📧 Trainer
        if (trainerData?.email) {
            await this.mailService.sendMail({
                to: trainerData.email.toString(),
                subject: `New Class Scheduled: ${context.className}`,
                template: 'class-schedule-created-notification',
                context,
            });
        }

        // 📧 Organization
        if (organizationDetail?.email) {
            await this.mailService.sendMail({
                to: organizationDetail.email.toString(),
                subject: `New Class Scheduled: ${context.className}`,
                template: 'class-schedule-created-notification',
                context,
            });
        }

        // 📧 Staff
        for (const staff of staffList) {
            if (staff.email) {
                await this.mailService.sendMail({
                    to: staff.email.toString(),
                    subject: `New Class Scheduled: ${context.className}`,
                    template: 'class-schedule-created-notification',
                    context,
                });
            }
        }

        console.log(`Class schedule notification emails sent.`);
    }

    private async getALltheStaffs(facilityId: Types.ObjectId): Promise<any[]> {
        const commonPipeline: any[] = [
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "_id",
                    foreignField: "userId",
                    as: "staffDetails",
                    pipeline: [{ $match: { facilityId: { $in: [facilityId] } } }],
                },
            },
            { $unwind: { path: "$staffDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: 'role',
                    localField: "role",
                    foreignField: "_id",
                    as: "roleDetails",
                    pipeline: [
                        {
                            $match: {
                                type: { $in: [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER] },
                            },
                        },
                    ],
                }
            },
            { $unwind: { path: "$roleDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "facilities",
                    localField: "staffDetails.facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            { $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: true } },
        ];
        commonPipeline.push({
            $group: {
                _id: "$staffDetails._id",
                gender: { $first: "$staffDetails.gender" },
                profilePicture: { $first: "$staffDetails.profilePicture" },
                userId: { $first: "$_id" },
                firstName: { $first: "$firstName" },
                lastName: { $first: "$lastName" },
                mobile: { $first: "$mobile" },
                email: { $first: "$email" },
                role: { $first: "$role" },
                isActive: { $first: "$isActive" },
                createdAt: { $first: "$createdAt" },
                facilityNames: { $push: "$facilityDetails.facilityName" },
            },
        });
        commonPipeline.push(
            {
                $sort: {
                    isActive: -1,
                    updatedAt: -1,
                },
            },
            {
                $facet: {
                    metadata: [{ $count: "total" }],
                    data: [
                        {
                            $project: {
                                _id: 1,
                                userId: 1,
                                firstName: 1,
                                lastName: 1,
                                facilityNames: 1,
                                mobile: 1,
                                email: 1,
                                role: 1,
                                isActive: 1,
                                createdAt: 1,
                                gender: 1,
                                profilePicture: 1,
                            },
                        },

                    ],
                },
            },
        );

        // Execute aggregation
        const result = await this.UserModel.aggregate(commonPipeline);
        return result[0]?.data || []

    }
    private async sendClassEnrollmentConfirmationEmail(
        clients: (Omit<Clients, 'userId'> & { userId: UserDocument })[],
        schedule: Scheduling
    ): Promise<void> {
        try {
            const service = await this.ServiceModel.findOne({ _id: schedule.serviceCategoryId }).lean();
            const subType = service?.appointmentType?.find(
                (item: any) => item._id.toString() === schedule.subTypeId?.toString()
            );

            const trainerData = await this.UserModel.findOne({ _id: schedule.trainerId }).lean();
            const organizationDetail = await this.UserModel.findOne({ _id: schedule.organizationId }).lean();
            const staffList = await this.getALltheStaffs(new Types.ObjectId(schedule.facilityId));

            for (const client of clients) {
                const context = {
                    clientName: client.userId?.name || "Client",
                    className: subType?.name || service?.name || "Class",
                    date: schedule.date,
                    from: schedule.from,
                    to: schedule.to,
                    trainerName: trainerData
                        ? `${trainerData.firstName} ${trainerData.lastName}`
                        : "Unknown Trainer",
                };

                if (client.userId?.email) {
                    await this.mailService.sendMail({
                        to: client.userId.email.toString(),
                        subject: `You're enrolled in ${context.className}`,
                        template: 'class-enrolled-confirmation',
                        context,
                    });
                }

                if (organizationDetail?.email) {
                    await this.mailService.sendMail({
                        to: organizationDetail.email.toString(),
                        subject: `New Enrollment in ${context.className}`,
                        template: 'class-enrollment-notification',
                        context,
                    });
                }

                for (const staff of staffList) {
                    console.log(staff.email, "staff email")
                    if (staff.email) {
                        await this.mailService.sendMail({
                            to: staff.email.toString(),
                            subject: `New Enrollment in ${context.className}`,
                            template: 'class-enrollment-notification',
                            context,
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Failed to send enrollment emails:', error);
        }
    }

}
