import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsEnum, IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, Matches, Min } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { ScheduleDayDto } from "./time-slots.schedule.dto";
import moment from "moment";

export class ClassScheduleCreateDtoV1 {

    @ApiProperty({
        description: "Facility ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    trainerId: string;

    @ApiProperty({
        description: "Type of the Class.",
        enum: ClassType,
        example: ClassType.COURSES,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "Service ID if any service is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    subType?: string;

    @ApiProperty({
        description: "Appointment Id any package is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    serviceCategory: string;

    @ApiProperty({
        description: "Duration of the Session",
        example: 60,
    })
    @IsNotEmpty()
    @IsNumber()
    @Min(1)
    @Type(() => Number)
    duration: number;

    @ApiProperty({
        description: "Room Id for the course",
        type: String,
        required: true,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    roomId: string;

    @ApiProperty({
        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    @IsNotEmpty({ message: "Date range is required" })
    dateRange: DateRange;

    @ApiProperty({
        description: "Date of the course",
        type: Date,
        example: new Date().toISOString().split("T")[0],
    })
    @IsNotEmpty()
    @Type(() => Date)
    date: Date;

    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsOptional()
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsOptional()
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;

    @ApiProperty({
        description: "Note for course",
        example: 60,
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({
        description: "Capacity of the class",
        example: 20,
    })
    @Min(1, { message: "Capacity must be at least 1" })
    @Type(() => Number)
    capacity: number;
}


export class ClassScheduleCreateDto {

    @ApiProperty({
        description: "Facility ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    trainerId: string;

    @ApiHideProperty()
    @IsOptional()
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType = ClassType.CLASSES;

    @ApiProperty({
        description: "Service ID if any service is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    subType?: string;

    @ApiProperty({
        description: "Appointment Id any package is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    serviceCategory: string;

    @ApiProperty({
        description: "Room for the class",
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    @Type(() => String)
    roomId?: string;

    @ApiProperty({
        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    @IsNotEmpty({ message: "Date range is required" })
    dateRange: DateRange;

    @ApiProperty({
        description: "Mark Type for the course",
        enum: MarkAvailabilityType,
        example: MarkAvailabilityType.WEEKLY,
    })
    @IsEnum(MarkAvailabilityType, { message: "Mark type must be a valid recurring type" })
    markType?: MarkAvailabilityType;

    @ApiProperty({
        description: "Date of the course",
        type: Date,
        example: new Date().toISOString().split("T")[0],
    })
    @IsNotEmpty()
    @Transform(({ value }) => (value ? new Date(value) : null), { toClassOnly: true })
    @Type(() => Date)
    startDate: Date;

    @ApiProperty({
        description: "End Date of the course",
        type: Date,
        example: new Date().toISOString().split("T")[0],
    })
    @IsOptional()
    @Transform(({ value }) => (value ? new Date(value) : null), { toClassOnly: true })
    @Type(() => Date)
    endDate?: Date;

    @ApiProperty({
        description: "The schedule for classes booking",
        type: ScheduleDayDto,
    })
    @IsNotEmpty({ message: "Schedule is required" })
    schedule: ScheduleDayDto;

    @ApiProperty({
        description: "Note for course",
        example: 60,
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({
        description: "Capacity of the class",
        example: 20,
    })
    @IsOptional()
    @Min(1, { message: "Capacity must be at least 1" })
    @Type(() => Number)
    capacity?: number;
}