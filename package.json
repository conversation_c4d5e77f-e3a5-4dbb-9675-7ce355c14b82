{"name": "template", "version": "2.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": " nest start --watch", "start:debug": "nest start --debug --watch", "start:staging": "pm2 start dist/src/main.js --name gym-staging", "deploy:staging": "npm run build && pm2 delete gym-staging && pm2 start dist/src/main.js --name gym-staging", "start:preprod": "pm2 start dist/src/main.js --name gym-preprod", "deploy:preprod": "npm run build && pm2 delete gym-preprod && pm2 start dist/src/main.js --name gym-preprod", "deploy:development": "npm run build && pm2 restart ecosystem.dev.config.js --update-env", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:seed:permissions": "APP_ENV=migration nestjs-command seed:permission", "migrate:seed:policy": "APP_ENV=migration nestjs-command seed:policy", "migrate:seed:role": "APP_ENV=migration nestjs-command seed:role", "migrate:user:role": "APP_ENV=migration nestjs-command user:role", "migrate:seed:ppr": "yarn migrate:seed:permissions && yarn migrate:seed:policy && yarn migrate:seed:role"}, "dependencies": {"@casl/ability": "^6.7.3", "@faker-js/faker": "^9.5.1", "@keyv/redis": "^4.3.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "^11.0.9", "@nestjs/swagger": "^7.4.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^5.1.1", "@nestjs/websockets": "^11.0.9", "aws-sdk": "^2.1413.0", "axios": "^1.4.0", "bcrypt": "^5.1.0", "bcryptjs": "^3.0.2", "bullmq": "^5.44.0", "cache-manager": "^6.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.7.4", "connect-redis": "^8.0.2", "crypto-js": "^4.2.0", "csvtojson": "^2.0.10", "date-fns": "^4.1.0", "express-session": "^1.18.1", "fs-extra": "^11.3.0", "handlebars": "^4.7.8", "helmet": "^7.1.0", "jszip": "^3.10.1", "libphonenumber-js": "^1.12.8", "luxon": "^3.5.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "mongoose": "^7.3.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nestjs-command": "^3.1.4", "nestjs-i18n": "^10.5.0", "nestjs-pino": "^4.3.1", "node-cron": "^3.0.3", "number-to-words": "^1.2.4", "passport": "^0.6.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "puppeteer": "^24.1.0", "qrcode": "^1.5.4", "razorpay": "^2.9.6", "reflect-metadata": "^0.1.13", "response-time": "^2.3.3", "rxjs": "^7.8.2", "slugify": "^1.6.6", "string-width": "^7.2.0", "string-width-cjs": "5.1.1", "strip-ansi": "6.0.1", "strip-ansi-cjs": "8.0.0 ", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.13", "@types/express-session": "^1.18.1", "@types/jest": "29.5.0", "@types/multer": "^1.4.7", "@types/node": "18.15.11", "@types/passport-facebook": "^2.1.11", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.11", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.8.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}